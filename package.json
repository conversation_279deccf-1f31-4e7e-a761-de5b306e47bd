{"name": "product-catalog-tracker", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~49.0.0", "react": "18.2.0", "react-native": "0.72.6", "@react-native-async-storage/async-storage": "1.18.2"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}