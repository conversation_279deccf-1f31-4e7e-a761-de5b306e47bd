import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  Alert,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const App = () => {
  const [currentScreen, setCurrentScreen] = useState('Home');
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Form states
  const [productName, setProductName] = useState('');
  const [productCategory, setProductCategory] = useState('');
  const [productPrice, setProductPrice] = useState('');

  // Load products from storage on app start
  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      const storedProducts = await AsyncStorage.getItem('products');
      if (storedProducts) {
        setProducts(JSON.parse(storedProducts));
      }
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  const saveProducts = async (updatedProducts) => {
    try {
      await AsyncStorage.setItem('products', JSON.stringify(updatedProducts));
      setProducts(updatedProducts);
    } catch (error) {
      console.error('Error saving products:', error);
    }
  };

  const addProduct = () => {
    if (!productName || !productCategory || !productPrice) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    const price = parseFloat(productPrice);
    if (isNaN(price) || price <= 0) {
      Alert.alert('Error', 'Please enter a valid price');
      return;
    }

    const currentDate = new Date().toLocaleDateString();
    const newProduct = {
      id: Date.now().toString(),
      name: productName,
      category: productCategory,
      currentPrice: price,
      priceHistory: [
        {
          price: price,
          date: currentDate,
        },
      ],
      dateAdded: currentDate,
    };

    // Check if product already exists
    const existingProduct = products.find(
      (p) => p.name.toLowerCase() === productName.toLowerCase()
    );

    if (existingProduct) {
      // Update existing product with new price
      const updatedProducts = products.map((p) => {
        if (p.id === existingProduct.id) {
          return {
            ...p,
            currentPrice: price,
            priceHistory: [
              ...p.priceHistory,
              {
                price: price,
                date: currentDate,
              },
            ],
          };
        }
        return p;
      });
      saveProducts(updatedProducts);
      Alert.alert('Success', 'Product price updated!');
    } else {
      // Add new product
      const updatedProducts = [...products, newProduct];
      saveProducts(updatedProducts);
      Alert.alert('Success', 'Product added successfully!');
    }

    // Clear form
    setProductName('');
    setProductCategory('');
    setProductPrice('');
    setCurrentScreen('Home');
  };

  const deleteProduct = (productId) => {
    Alert.alert(
      'Delete Product',
      'Are you sure you want to delete this product?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const updatedProducts = products.filter((p) => p.id !== productId);
            saveProducts(updatedProducts);
          },
        },
      ]
    );
  };

  const filteredProducts = products.filter((product) =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Home Screen
  const HomeScreen = () => (
    <View style={styles.container}>
      <Text style={styles.title}>Product Catalog & Price Tracker</Text>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={() => setCurrentScreen('AddProduct')}
        >
          <Text style={styles.buttonText}>Add Product</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.button}
          onPress={() => setCurrentScreen('ViewProducts')}
        >
          <Text style={styles.buttonText}>View Products</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.button}
          onPress={() => setCurrentScreen('Search')}
        >
          <Text style={styles.buttonText}>Search Products</Text>
        </TouchableOpacity>
      </View>
      <Text style={styles.statsText}>Total Products: {products.length}</Text>
    </View>
  );

  // Add Product Screen
  const AddProductScreen = () => (
    <View style={styles.container}>
      <Text style={styles.title}>Add Product</Text>
      <TextInput
        style={styles.input}
        placeholder="Product Name"
        value={productName}
        onChangeText={setProductName}
      />
      <TextInput
        style={styles.input}
        placeholder="Category"
        value={productCategory}
        onChangeText={setProductCategory}
      />
      <TextInput
        style={styles.input}
        placeholder="Price"
        value={productPrice}
        onChangeText={setProductPrice}
        keyboardType="numeric"
      />
      <View style={styles.buttonRow}>
        <TouchableOpacity style={styles.button} onPress={addProduct}>
          <Text style={styles.buttonText}>Save Product</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={() => setCurrentScreen('Home')}
        >
          <Text style={styles.buttonText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // View Products Screen
  const ViewProductsScreen = () => (
    <View style={styles.container}>
      <Text style={styles.title}>All Products</Text>
      <FlatList
        data={products}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View style={styles.productItem}>
            <TouchableOpacity
              onPress={() => {
                setSelectedProduct(item);
                setCurrentScreen('PriceHistory');
              }}
            >
              <Text style={styles.productName}>{item.name}</Text>
              <Text style={styles.productCategory}>{item.category}</Text>
              <Text style={styles.productPrice}>${item.currentPrice.toFixed(2)}</Text>
              <Text style={styles.productDate}>Added: {item.dateAdded}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => deleteProduct(item.id)}
            >
              <Text style={styles.deleteButtonText}>Delete</Text>
            </TouchableOpacity>
          </View>
        )}
        ListEmptyComponent={
          <Text style={styles.emptyText}>No products added yet</Text>
        }
      />
      <TouchableOpacity
        style={[styles.button, styles.secondaryButton]}
        onPress={() => setCurrentScreen('Home')}
      >
        <Text style={styles.buttonText}>Back to Home</Text>
      </TouchableOpacity>
    </View>
  );

  // Search Screen
  const SearchScreen = () => (
    <View style={styles.container}>
      <Text style={styles.title}>Search Products</Text>
      <TextInput
        style={styles.input}
        placeholder="Search by name or category..."
        value={searchQuery}
        onChangeText={setSearchQuery}
      />
      <FlatList
        data={filteredProducts}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.productItem}
            onPress={() => {
              setSelectedProduct(item);
              setCurrentScreen('PriceHistory');
            }}
          >
            <Text style={styles.productName}>{item.name}</Text>
            <Text style={styles.productCategory}>{item.category}</Text>
            <Text style={styles.productPrice}>${item.currentPrice.toFixed(2)}</Text>
            <Text style={styles.productDate}>Added: {item.dateAdded}</Text>
          </TouchableOpacity>
        )}
        ListEmptyComponent={
          <Text style={styles.emptyText}>
            {searchQuery ? 'No products found' : 'Start typing to search'}
          </Text>
        }
      />
      <TouchableOpacity
        style={[styles.button, styles.secondaryButton]}
        onPress={() => {
          setSearchQuery('');
          setCurrentScreen('Home');
        }}
      >
        <Text style={styles.buttonText}>Back to Home</Text>
      </TouchableOpacity>
    </View>
  );

  // Price History Screen
  const PriceHistoryScreen = () => (
    <View style={styles.container}>
      <Text style={styles.title}>Price History</Text>
      {selectedProduct && (
        <>
          <Text style={styles.productName}>{selectedProduct.name}</Text>
          <Text style={styles.productCategory}>{selectedProduct.category}</Text>
          <Text style={styles.currentPriceLabel}>
            Current Price: ${selectedProduct.currentPrice.toFixed(2)}
          </Text>
          <FlatList
            data={selectedProduct.priceHistory}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item }) => (
              <View style={styles.historyItem}>
                <Text style={styles.historyPrice}>${item.price.toFixed(2)}</Text>
                <Text style={styles.historyDate}>{item.date}</Text>
              </View>
            )}
            ListEmptyComponent={
              <Text style={styles.emptyText}>No price history available</Text>
            }
          />
        </>
      )}
      <TouchableOpacity
        style={[styles.button, styles.secondaryButton]}
        onPress={() => setCurrentScreen('ViewProducts')}
      >
        <Text style={styles.buttonText}>Back to Products</Text>
      </TouchableOpacity>
    </View>
  );

  // Main render function
  const renderScreen = () => {
    switch (currentScreen) {
      case 'AddProduct':
        return <AddProductScreen />;
      case 'ViewProducts':
        return <ViewProductsScreen />;
      case 'Search':
        return <SearchScreen />;
      case 'PriceHistory':
        return <PriceHistoryScreen />;
      default:
        return <HomeScreen />;
    }
  };

  return <SafeAreaView style={styles.safeArea}>{renderScreen()}</SafeAreaView>;
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    alignItems: 'center',
  },
  secondaryButton: {
    backgroundColor: '#6c757d',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    backgroundColor: 'white',
    fontSize: 16,
  },
  productItem: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  productName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  productCategory: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 5,
  },
  productDate: {
    fontSize: 12,
    color: '#999',
  },
  deleteButton: {
    backgroundColor: '#dc3545',
    padding: 8,
    borderRadius: 5,
    alignSelf: 'flex-end',
    marginTop: 10,
  },
  deleteButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 50,
  },
  statsText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 20,
  },
  currentPriceLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
    textAlign: 'center',
    marginBottom: 20,
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  historyPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  historyDate: {
    fontSize: 14,
    color: '#666',
  },
});

export default App;
